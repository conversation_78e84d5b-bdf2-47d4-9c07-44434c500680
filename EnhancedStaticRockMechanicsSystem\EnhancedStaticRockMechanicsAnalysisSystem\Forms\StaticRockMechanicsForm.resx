<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lblTitle.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lblTitle.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 18pt, style=Bold</value>
  </data>
  <data name="lblTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lblTitle.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>2564, 120</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lblTitle.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblTitle.Text" xml:space="preserve">
    <value>增强型静态岩石力学参数法脆性指数分析系统</value>
  </data>
  <data name="lblTitle.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;lblTitle.Name" xml:space="preserve">
    <value>lblTitle</value>
  </data>
  <data name="&gt;&gt;lblTitle.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblTitle.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblTitle.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lblWelcome.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblWelcome.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="lblWelcome.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 140</value>
  </data>
  <data name="lblWelcome.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblWelcome.Size" type="System.Drawing.Size, System.Drawing">
    <value>398, 31</value>
  </data>
  <data name="lblWelcome.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lblWelcome.Text" xml:space="preserve">
    <value>欢迎使用增强型静态岩石力学参数法</value>
  </data>
  <data name="&gt;&gt;lblWelcome.Name" xml:space="preserve">
    <value>lblWelcome</value>
  </data>
  <data name="&gt;&gt;lblWelcome.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblWelcome.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblWelcome.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="btnBack.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnBack.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="btnBack.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 205</value>
  </data>
  <data name="btnBack.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnBack.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 70</value>
  </data>
  <data name="btnBack.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnBack.Text" xml:space="preserve">
    <value>返回主界面</value>
  </data>
  <data name="&gt;&gt;btnBack.Name" xml:space="preserve">
    <value>btnBack</value>
  </data>
  <data name="&gt;&gt;btnBack.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnBack.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnBack.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="btnLogout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnLogout.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="btnLogout.Location" type="System.Drawing.Point, System.Drawing">
    <value>297, 205</value>
  </data>
  <data name="btnLogout.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnLogout.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 70</value>
  </data>
  <data name="btnLogout.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnLogout.Text" xml:space="preserve">
    <value>退出登录</value>
  </data>
  <data name="&gt;&gt;btnLogout.Name" xml:space="preserve">
    <value>btnLogout</value>
  </data>
  <data name="&gt;&gt;btnLogout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnLogout.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnLogout.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnEmergencyExit.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnEmergencyExit.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="btnEmergencyExit.Location" type="System.Drawing.Point, System.Drawing">
    <value>540, 205</value>
  </data>
  <data name="btnEmergencyExit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnEmergencyExit.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 70</value>
  </data>
  <data name="btnEmergencyExit.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="btnEmergencyExit.Text" xml:space="preserve">
    <value>存为对比图</value>
  </data>
  <data name="&gt;&gt;btnEmergencyExit.Name" xml:space="preserve">
    <value>btnEmergencyExit</value>
  </data>
  <data name="&gt;&gt;btnEmergencyExit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEmergencyExit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnEmergencyExit.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnEnhancedAnalysis.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnEnhancedAnalysis.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 6.666667pt, style=Bold</value>
  </data>
  <data name="btnEnhancedAnalysis.Location" type="System.Drawing.Point, System.Drawing">
    <value>1938, 228</value>
  </data>
  <data name="btnEnhancedAnalysis.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnEnhancedAnalysis.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 64</value>
  </data>
  <data name="btnEnhancedAnalysis.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="btnEnhancedAnalysis.Text" xml:space="preserve">
    <value>增强分析算法</value>
  </data>
  <data name="&gt;&gt;btnEnhancedAnalysis.Name" xml:space="preserve">
    <value>btnEnhancedAnalysis</value>
  </data>
  <data name="&gt;&gt;btnEnhancedAnalysis.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEnhancedAnalysis.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;btnEnhancedAnalysis.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnViewComparison.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnViewComparison.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 6.666667pt, style=Bold</value>
  </data>
  <data name="btnViewComparison.Location" type="System.Drawing.Point, System.Drawing">
    <value>1369, 225</value>
  </data>
  <data name="btnViewComparison.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnViewComparison.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 64</value>
  </data>
  <data name="btnViewComparison.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="btnViewComparison.Text" xml:space="preserve">
    <value>查看对比图</value>
  </data>
  <data name="&gt;&gt;btnViewComparison.Name" xml:space="preserve">
    <value>btnViewComparison</value>
  </data>
  <data name="&gt;&gt;btnViewComparison.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnViewComparison.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;btnViewComparison.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pnlParameters.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="rbVelocityDts.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbVelocityDts.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 107</value>
  </data>
  <data name="rbVelocityDts.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="rbVelocityDts.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 28</value>
  </data>
  <data name="rbVelocityDts.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="rbVelocityDts.Text" xml:space="preserve">
    <value>DTS (μs/m)</value>
  </data>
  <data name="&gt;&gt;rbVelocityDts.Name" xml:space="preserve">
    <value>rbVelocityDts</value>
  </data>
  <data name="&gt;&gt;rbVelocityDts.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbVelocityDts.Parent" xml:space="preserve">
    <value>grpVs</value>
  </data>
  <data name="&gt;&gt;rbVelocityDts.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rbVelocityVs.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbVelocityVs.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 54</value>
  </data>
  <data name="rbVelocityVs.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="rbVelocityVs.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 28</value>
  </data>
  <data name="rbVelocityVs.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="rbVelocityVs.Text" xml:space="preserve">
    <value>Vs (m/s)</value>
  </data>
  <data name="&gt;&gt;rbVelocityVs.Name" xml:space="preserve">
    <value>rbVelocityVs</value>
  </data>
  <data name="&gt;&gt;rbVelocityVs.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbVelocityVs.Parent" xml:space="preserve">
    <value>grpVs</value>
  </data>
  <data name="&gt;&gt;rbVelocityVs.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grpVs.Location" type="System.Drawing.Point, System.Drawing">
    <value>1938, 42</value>
  </data>
  <data name="grpVs.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="grpVs.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="grpVs.Size" type="System.Drawing.Size, System.Drawing">
    <value>251, 171</value>
  </data>
  <data name="grpVs.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="grpVs.Text" xml:space="preserve">
    <value>横波速度单位</value>
  </data>
  <data name="&gt;&gt;grpVs.Name" xml:space="preserve">
    <value>grpVs</value>
  </data>
  <data name="&gt;&gt;grpVs.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;grpVs.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;grpVs.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="rbVelocityDt.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbVelocityDt.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 107</value>
  </data>
  <data name="rbVelocityDt.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="rbVelocityDt.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 28</value>
  </data>
  <data name="rbVelocityDt.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="rbVelocityDt.Text" xml:space="preserve">
    <value>DT (μs/m）</value>
  </data>
  <data name="&gt;&gt;rbVelocityDt.Name" xml:space="preserve">
    <value>rbVelocityDt</value>
  </data>
  <data name="&gt;&gt;rbVelocityDt.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbVelocityDt.Parent" xml:space="preserve">
    <value>grpVp</value>
  </data>
  <data name="&gt;&gt;rbVelocityDt.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rbVelocityVp.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbVelocityVp.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 54</value>
  </data>
  <data name="rbVelocityVp.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="rbVelocityVp.Size" type="System.Drawing.Size, System.Drawing">
    <value>109, 28</value>
  </data>
  <data name="rbVelocityVp.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="rbVelocityVp.Text" xml:space="preserve">
    <value>Vp (m/s)</value>
  </data>
  <data name="&gt;&gt;rbVelocityVp.Name" xml:space="preserve">
    <value>rbVelocityVp</value>
  </data>
  <data name="&gt;&gt;rbVelocityVp.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbVelocityVp.Parent" xml:space="preserve">
    <value>grpVp</value>
  </data>
  <data name="&gt;&gt;rbVelocityVp.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grpVp.Location" type="System.Drawing.Point, System.Drawing">
    <value>1636, 42</value>
  </data>
  <data name="grpVp.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="grpVp.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="grpVp.Size" type="System.Drawing.Size, System.Drawing">
    <value>251, 171</value>
  </data>
  <data name="grpVp.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="grpVp.Text" xml:space="preserve">
    <value>纵波速度单位</value>
  </data>
  <data name="&gt;&gt;grpVp.Name" xml:space="preserve">
    <value>grpVp</value>
  </data>
  <data name="&gt;&gt;grpVp.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;grpVp.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;grpVp.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="rbDensityRhob.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbDensityRhob.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 107</value>
  </data>
  <data name="rbDensityRhob.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="rbDensityRhob.Size" type="System.Drawing.Size, System.Drawing">
    <value>157, 28</value>
  </data>
  <data name="rbDensityRhob.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rbDensityRhob.Text" xml:space="preserve">
    <value>RHOB (g/cm³)</value>
  </data>
  <data name="&gt;&gt;rbDensityRhob.Name" xml:space="preserve">
    <value>rbDensityRhob</value>
  </data>
  <data name="&gt;&gt;rbDensityRhob.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbDensityRhob.Parent" xml:space="preserve">
    <value>grpDensity</value>
  </data>
  <data name="&gt;&gt;rbDensityRhob.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rbDensityRho.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbDensityRho.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 54</value>
  </data>
  <data name="rbDensityRho.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="rbDensityRho.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 28</value>
  </data>
  <data name="rbDensityRho.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rbDensityRho.Text" xml:space="preserve">
    <value>ρ (g/cm³)</value>
  </data>
  <data name="&gt;&gt;rbDensityRho.Name" xml:space="preserve">
    <value>rbDensityRho</value>
  </data>
  <data name="&gt;&gt;rbDensityRho.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbDensityRho.Parent" xml:space="preserve">
    <value>grpDensity</value>
  </data>
  <data name="&gt;&gt;rbDensityRho.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grpDensity.Location" type="System.Drawing.Point, System.Drawing">
    <value>1323, 42</value>
  </data>
  <data name="grpDensity.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="grpDensity.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="grpDensity.Size" type="System.Drawing.Size, System.Drawing">
    <value>251, 171</value>
  </data>
  <data name="grpDensity.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="grpDensity.Text" xml:space="preserve">
    <value>密度单位</value>
  </data>
  <data name="&gt;&gt;grpDensity.Name" xml:space="preserve">
    <value>grpDensity</value>
  </data>
  <data name="&gt;&gt;grpDensity.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;grpDensity.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;grpDensity.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lblCalculationResult.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="lblCalculationResult.Location" type="System.Drawing.Point, System.Drawing">
    <value>293, 170</value>
  </data>
  <data name="lblCalculationResult.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblCalculationResult.Size" type="System.Drawing.Size, System.Drawing">
    <value>1018, 60</value>
  </data>
  <data name="lblCalculationResult.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lblCalculationResult.Text" xml:space="preserve">
    <value>计算结果将在此显示</value>
  </data>
  <data name="lblCalculationResult.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;lblCalculationResult.Name" xml:space="preserve">
    <value>lblCalculationResult</value>
  </data>
  <data name="&gt;&gt;lblCalculationResult.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblCalculationResult.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;lblCalculationResult.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="btnCalculate.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnCalculate.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="btnCalculate.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 170</value>
  </data>
  <data name="btnCalculate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnCalculate.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 60</value>
  </data>
  <data name="btnCalculate.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="btnCalculate.Text" xml:space="preserve">
    <value>计算脆性指数</value>
  </data>
  <data name="&gt;&gt;btnCalculate.Name" xml:space="preserve">
    <value>btnCalculate</value>
  </data>
  <data name="&gt;&gt;btnCalculate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCalculate.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;btnCalculate.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtVs.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="txtVs.Location" type="System.Drawing.Point, System.Drawing">
    <value>1111, 100</value>
  </data>
  <data name="txtVs.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="txtVs.Size" type="System.Drawing.Size, System.Drawing">
    <value>166, 34</value>
  </data>
  <data name="txtVs.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;txtVs.Name" xml:space="preserve">
    <value>txtVs</value>
  </data>
  <data name="&gt;&gt;txtVs.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtVs.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;txtVs.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lblVs.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblVs.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="lblVs.Location" type="System.Drawing.Point, System.Drawing">
    <value>899, 104</value>
  </data>
  <data name="lblVs.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblVs.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 27</value>
  </data>
  <data name="lblVs.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lblVs.Text" xml:space="preserve">
    <value>横波速度 Vs (m/s):</value>
  </data>
  <data name="&gt;&gt;lblVs.Name" xml:space="preserve">
    <value>lblVs</value>
  </data>
  <data name="&gt;&gt;lblVs.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblVs.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;lblVs.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtVp.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="txtVp.Location" type="System.Drawing.Point, System.Drawing">
    <value>659, 100</value>
  </data>
  <data name="txtVp.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="txtVp.Size" type="System.Drawing.Size, System.Drawing">
    <value>187, 34</value>
  </data>
  <data name="txtVp.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;txtVp.Name" xml:space="preserve">
    <value>txtVp</value>
  </data>
  <data name="&gt;&gt;txtVp.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtVp.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;txtVp.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lblVp.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblVp.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="lblVp.Location" type="System.Drawing.Point, System.Drawing">
    <value>449, 104</value>
  </data>
  <data name="lblVp.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblVp.Size" type="System.Drawing.Size, System.Drawing">
    <value>187, 27</value>
  </data>
  <data name="lblVp.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lblVp.Text" xml:space="preserve">
    <value>纵波速度 Vp (m/s):</value>
  </data>
  <data name="&gt;&gt;lblVp.Name" xml:space="preserve">
    <value>lblVp</value>
  </data>
  <data name="&gt;&gt;lblVp.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblVp.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;lblVp.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="txtDensity.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="txtDensity.Location" type="System.Drawing.Point, System.Drawing">
    <value>258, 100</value>
  </data>
  <data name="txtDensity.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="txtDensity.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 34</value>
  </data>
  <data name="txtDensity.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;txtDensity.Name" xml:space="preserve">
    <value>txtDensity</value>
  </data>
  <data name="&gt;&gt;txtDensity.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtDensity.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;txtDensity.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="lblDensity.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblDensity.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 10pt</value>
  </data>
  <data name="lblDensity.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 104</value>
  </data>
  <data name="lblDensity.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblDensity.Size" type="System.Drawing.Size, System.Drawing">
    <value>195, 27</value>
  </data>
  <data name="lblDensity.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lblDensity.Text" xml:space="preserve">
    <value>岩石密度 ρ (g/cm³):</value>
  </data>
  <data name="&gt;&gt;lblDensity.Name" xml:space="preserve">
    <value>lblDensity</value>
  </data>
  <data name="&gt;&gt;lblDensity.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblDensity.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;lblDensity.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="lblParametersTitle.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblParametersTitle.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt, style=Bold</value>
  </data>
  <data name="lblParametersTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 20</value>
  </data>
  <data name="lblParametersTitle.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblParametersTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>206, 31</value>
  </data>
  <data name="lblParametersTitle.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblParametersTitle.Text" xml:space="preserve">
    <value>岩石力学参数输入</value>
  </data>
  <data name="&gt;&gt;lblParametersTitle.Name" xml:space="preserve">
    <value>lblParametersTitle</value>
  </data>
  <data name="&gt;&gt;lblParametersTitle.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblParametersTitle.Parent" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;lblParametersTitle.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="pnlParameters.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 288</value>
  </data>
  <data name="pnlParameters.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="pnlParameters.Size" type="System.Drawing.Size, System.Drawing">
    <value>2496, 300</value>
  </data>
  <data name="pnlParameters.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;pnlParameters.Name" xml:space="preserve">
    <value>pnlParameters</value>
  </data>
  <data name="&gt;&gt;pnlParameters.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlParameters.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlParameters.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pnlChart.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="chartBrittleness.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="chartBrittleness.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 90</value>
  </data>
  <data name="chartBrittleness.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="chartBrittleness.Size" type="System.Drawing.Size, System.Drawing">
    <value>1354, 580</value>
  </data>
  <data name="chartBrittleness.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;chartBrittleness.Name" xml:space="preserve">
    <value>chartBrittleness</value>
  </data>
  <data name="&gt;&gt;chartBrittleness.Type" xml:space="preserve">
    <value>System.Windows.Forms.DataVisualization.Charting.Chart, System.Windows.Forms.DataVisualization, Culture=neutral, PublicKeyToken=31bf3856ad364e35</value>
  </data>
  <data name="&gt;&gt;chartBrittleness.Parent" xml:space="preserve">
    <value>pnlChart</value>
  </data>
  <data name="&gt;&gt;chartBrittleness.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnSaveCurve.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnSaveCurve.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnSaveCurve.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="btnSaveCurve.Location" type="System.Drawing.Point, System.Drawing">
    <value>1172, 10</value>
  </data>
  <data name="btnSaveCurve.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnSaveCurve.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 60</value>
  </data>
  <data name="btnSaveCurve.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnSaveCurve.Text" xml:space="preserve">
    <value>保存曲线</value>
  </data>
  <data name="&gt;&gt;btnSaveCurve.Name" xml:space="preserve">
    <value>btnSaveCurve</value>
  </data>
  <data name="&gt;&gt;btnSaveCurve.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnSaveCurve.Parent" xml:space="preserve">
    <value>pnlChart</value>
  </data>
  <data name="&gt;&gt;btnSaveCurve.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnReset.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnReset.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnReset.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="btnReset.Location" type="System.Drawing.Point, System.Drawing">
    <value>963, 10</value>
  </data>
  <data name="btnReset.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnReset.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 60</value>
  </data>
  <data name="btnReset.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnReset.Text" xml:space="preserve">
    <value>重置</value>
  </data>
  <data name="&gt;&gt;btnReset.Name" xml:space="preserve">
    <value>btnReset</value>
  </data>
  <data name="&gt;&gt;btnReset.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnReset.Parent" xml:space="preserve">
    <value>pnlChart</value>
  </data>
  <data name="&gt;&gt;btnReset.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnGenerateCurve.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnGenerateCurve.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnGenerateCurve.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="btnGenerateCurve.Location" type="System.Drawing.Point, System.Drawing">
    <value>738, 8</value>
  </data>
  <data name="btnGenerateCurve.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnGenerateCurve.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 60</value>
  </data>
  <data name="btnGenerateCurve.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnGenerateCurve.Text" xml:space="preserve">
    <value>生成曲线</value>
  </data>
  <data name="&gt;&gt;btnGenerateCurve.Name" xml:space="preserve">
    <value>btnGenerateCurve</value>
  </data>
  <data name="&gt;&gt;btnGenerateCurve.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnGenerateCurve.Parent" xml:space="preserve">
    <value>pnlChart</value>
  </data>
  <data name="&gt;&gt;btnGenerateCurve.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblChartTitle.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblChartTitle.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt, style=Bold</value>
  </data>
  <data name="lblChartTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 20</value>
  </data>
  <data name="lblChartTitle.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblChartTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>182, 31</value>
  </data>
  <data name="lblChartTitle.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lblChartTitle.Text" xml:space="preserve">
    <value>脆性指数曲线图</value>
  </data>
  <data name="&gt;&gt;lblChartTitle.Name" xml:space="preserve">
    <value>lblChartTitle</value>
  </data>
  <data name="&gt;&gt;lblChartTitle.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblChartTitle.Parent" xml:space="preserve">
    <value>pnlChart</value>
  </data>
  <data name="&gt;&gt;lblChartTitle.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pnlChart.Location" type="System.Drawing.Point, System.Drawing">
    <value>1160, 600</value>
  </data>
  <data name="pnlChart.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="pnlChart.Size" type="System.Drawing.Size, System.Drawing">
    <value>1374, 728</value>
  </data>
  <data name="pnlChart.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;pnlChart.Name" xml:space="preserve">
    <value>pnlChart</value>
  </data>
  <data name="&gt;&gt;pnlChart.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlChart.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlChart.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pnlData.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left</value>
  </data>
  <data name="dgvMechanicsData.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="dgvMechanicsData.ColumnHeadersHeight" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="dgvMechanicsData.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 100</value>
  </data>
  <data name="dgvMechanicsData.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="dgvMechanicsData.RowHeadersWidth" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="dgvMechanicsData.Size" type="System.Drawing.Size, System.Drawing">
    <value>1061, 608</value>
  </data>
  <data name="dgvMechanicsData.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;dgvMechanicsData.Name" xml:space="preserve">
    <value>dgvMechanicsData</value>
  </data>
  <data name="&gt;&gt;dgvMechanicsData.Type" xml:space="preserve">
    <value>System.Windows.Forms.DataGridView, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;dgvMechanicsData.Parent" xml:space="preserve">
    <value>pnlData</value>
  </data>
  <data name="&gt;&gt;dgvMechanicsData.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnExport.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnExport.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnExport.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="btnExport.Location" type="System.Drawing.Point, System.Drawing">
    <value>895, 20</value>
  </data>
  <data name="btnExport.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnExport.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 60</value>
  </data>
  <data name="btnExport.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnExport.Text" xml:space="preserve">
    <value>导出数据</value>
  </data>
  <data name="&gt;&gt;btnExport.Name" xml:space="preserve">
    <value>btnExport</value>
  </data>
  <data name="&gt;&gt;btnExport.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnExport.Parent" xml:space="preserve">
    <value>pnlData</value>
  </data>
  <data name="&gt;&gt;btnExport.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnDataSearch.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDataSearch.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnDataSearch.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="btnDataSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>500, 20</value>
  </data>
  <data name="btnDataSearch.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnDataSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 60</value>
  </data>
  <data name="btnDataSearch.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnDataSearch.Text" xml:space="preserve">
    <value>数据搜索</value>
  </data>
  <data name="&gt;&gt;btnDataSearch.Name" xml:space="preserve">
    <value>btnDataSearch</value>
  </data>
  <data name="&gt;&gt;btnDataSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDataSearch.Parent" xml:space="preserve">
    <value>pnlData</value>
  </data>
  <data name="&gt;&gt;btnDataSearch.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnImport.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnImport.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnImport.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="btnImport.Location" type="System.Drawing.Point, System.Drawing">
    <value>693, 20</value>
  </data>
  <data name="btnImport.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="btnImport.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 60</value>
  </data>
  <data name="btnImport.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnImport.Text" xml:space="preserve">
    <value>导入数据</value>
  </data>
  <data name="&gt;&gt;btnImport.Name" xml:space="preserve">
    <value>btnImport</value>
  </data>
  <data name="&gt;&gt;btnImport.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnImport.Parent" xml:space="preserve">
    <value>pnlData</value>
  </data>
  <data name="&gt;&gt;btnImport.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lblDataTitle.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblDataTitle.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt, style=Bold</value>
  </data>
  <data name="lblDataTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 20</value>
  </data>
  <data name="lblDataTitle.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 0, 6, 0</value>
  </data>
  <data name="lblDataTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>206, 31</value>
  </data>
  <data name="lblDataTitle.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lblDataTitle.Text" xml:space="preserve">
    <value>岩石力学参数数据</value>
  </data>
  <data name="&gt;&gt;lblDataTitle.Name" xml:space="preserve">
    <value>lblDataTitle</value>
  </data>
  <data name="&gt;&gt;lblDataTitle.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblDataTitle.Parent" xml:space="preserve">
    <value>pnlData</value>
  </data>
  <data name="&gt;&gt;lblDataTitle.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="pnlData.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 600</value>
  </data>
  <data name="pnlData.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="pnlData.Size" type="System.Drawing.Size, System.Drawing">
    <value>1097, 728</value>
  </data>
  <data name="pnlData.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;pnlData.Name" xml:space="preserve">
    <value>pnlData</value>
  </data>
  <data name="&gt;&gt;pnlData.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlData.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlData.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>11, 24</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>2564, 1410</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>6, 6, 6, 6</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>静态岩石力学参数法 - 脆性指数计算</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>StaticRockMechanicsForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>