<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- 禁用默认项目项 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <!-- 禁用自动生成资源名称 -->
    <GenerateResourceMSBuildArchitecture>CurrentArchitecture</GenerateResourceMSBuildArchitecture>
    <GenerateResourceMSBuildRuntime>CurrentRuntime</GenerateResourceMSBuildRuntime>
    <!-- 强制使用显式指定的ManifestResourceName -->
    <EmbeddedResourceUseDependentUponConvention>false</EmbeddedResourceUseDependentUponConvention>
    <!-- 高DPI支持设置 -->
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <AssemblyTitle>增强版静态岩石力学参数分析系统</AssemblyTitle>
    <Product>增强版静态岩石力学参数分析系统</Product>
    <Description>基于静态岩石力学参数计算脆性指数的专业地质分析软件</Description>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <!-- 包引用 -->
  <ItemGroup>
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="EPPlus" Version="8.0.1" />
    <PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.OleDb" Version="9.0.4" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <!-- 源代码文件 -->
  <ItemGroup>
    <!-- 核心类 -->
    <Compile Include="Core\RockMechanicsCalculator.cs" />
    <Compile Include="Core\DataManager.cs" />
    <Compile Include="Core\FormAdapter.cs" />

    <!-- 模型类 -->
    <Compile Include="Models\RockMechanicsDataPoint.cs" />
    <Compile Include="Models\BrittlenessDataPoint.cs" />
    <Compile Include="Models\CalculationResult.cs" />
    <Compile Include="Models\RockMechanicsResult.cs" />

    <!-- 服务类 -->
    <Compile Include="Services\ImportService.cs" />
    <Compile Include="Services\ExportService.cs" />
    <Compile Include="Services\LoggingService.cs" />

    <!-- 窗体类 -->
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DashboardForm.Designer.cs">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\StaticRockMechanicsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StaticRockMechanicsForm.Designer.cs">
      <DependentUpon>StaticRockMechanicsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SimpleComparisonChartForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DataSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EnhancedAnalysisResultForm.cs">
      <SubType>Form</SubType>
    </Compile>

    <!-- 主程序 -->
    <Compile Include="Program.cs" />
    <Compile Include="AppConfig.cs" />
  </ItemGroup>

  <!-- 资源文件 -->
  <ItemGroup>
    <EmbeddedResource Include="Forms\DataSearchForm.resx" />
    <EmbeddedResource Include="Forms\EnhancedAnalysisResultForm.resx" />
    <EmbeddedResource Include="Forms\LoginForm.resx" />
    <EmbeddedResource Include="Forms\DashboardForm.resx" />
    <EmbeddedResource Include="Forms\SimpleComparisonChartForm.resx" />
    <EmbeddedResource Include="Forms\StaticRockMechanicsForm.resx" />
    <EmbeddedResource Include="Forms\StaticRockMechanicsForm.zh-Hans-CN.resx" />
  </ItemGroup>

</Project>
