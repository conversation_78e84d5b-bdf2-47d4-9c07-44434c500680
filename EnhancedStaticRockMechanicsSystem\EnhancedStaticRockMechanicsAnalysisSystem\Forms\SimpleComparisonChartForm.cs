using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;
using Newtonsoft.Json;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    /// <summary>
    /// 简化版对比图显示窗体 - 参考原系统ComparisonChartForm
    /// </summary>
    public partial class SimpleComparisonChartForm : Form
    {
        private Chart chartComparison;
        private Button btnClose;
        private Button btnSaveImage;
        private Button btnSeparate;
        private Button btnRestore;
        private Button btnImportData;
        private Label lblTitle;
        private Panel pnlControls;
        private bool isSeparated = false; // 标记是否处于分隔状态

        public SimpleComparisonChartForm()
        {
            InitializeComponent();
            InitializeChart();
        }

        private void InitializeComponent()
        {
            this.chartComparison = new Chart();
            this.btnClose = new Button();
            this.btnSaveImage = new Button();
            btnSeparate = new Button();
            btnRestore = new Button();
            btnImportData = new Button();
            lblTitle = new Label();
            pnlControls = new Panel();
            pnlControls.SuspendLayout();
            SuspendLayout();
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.FromArgb(50, 50, 50);
            btnClose.FlatAppearance.BorderColor = Color.Cyan;
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.ForeColor = Color.LightSkyBlue;
            btnClose.Location = new Point(1444, 24);
            btnClose.Margin = new Padding(4, 5, 4, 5);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(138, 48);
            btnClose.TabIndex = 1;
            btnClose.Text = "关闭";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += BtnClose_Click;
            // 
            // btnSaveImage
            // 
            btnSaveImage.BackColor = Color.FromArgb(50, 50, 50);
            btnSaveImage.FlatAppearance.BorderColor = Color.Cyan;
            btnSaveImage.FlatStyle = FlatStyle.Flat;
            btnSaveImage.ForeColor = Color.LightSkyBlue;
            btnSaveImage.Location = new Point(14, 24);
            btnSaveImage.Margin = new Padding(4, 5, 4, 5);
            btnSaveImage.Name = "btnSaveImage";
            btnSaveImage.Size = new Size(138, 48);
            btnSaveImage.TabIndex = 0;
            btnSaveImage.Text = "保存图像";
            btnSaveImage.UseVisualStyleBackColor = false;
            btnSaveImage.Click += BtnSaveImage_Click;
            // 
            // btnSeparate
            // 
            btnSeparate.BackColor = Color.FromArgb(50, 50, 50);
            btnSeparate.FlatAppearance.BorderColor = Color.Cyan;
            btnSeparate.FlatStyle = FlatStyle.Flat;
            btnSeparate.ForeColor = Color.LightSkyBlue;
            btnSeparate.Location = new Point(165, 24);
            btnSeparate.Margin = new Padding(4, 5, 4, 5);
            btnSeparate.Name = "btnSeparate";
            btnSeparate.Size = new Size(138, 48);
            btnSeparate.TabIndex = 2;
            btnSeparate.Text = "分隔显示";
            btnSeparate.UseVisualStyleBackColor = false;
            btnSeparate.Click += BtnSeparate_Click;
            // 
            // btnRestore
            // 
            btnRestore.BackColor = Color.FromArgb(50, 50, 50);
            btnRestore.FlatAppearance.BorderColor = Color.Cyan;
            btnRestore.FlatStyle = FlatStyle.Flat;
            btnRestore.ForeColor = Color.LightSkyBlue;
            btnRestore.Location = new Point(332, 24);
            btnRestore.Margin = new Padding(4, 5, 4, 5);
            btnRestore.Name = "btnRestore";
            btnRestore.Size = new Size(138, 48);
            btnRestore.TabIndex = 3;
            btnRestore.Text = "恢复显示";
            btnRestore.UseVisualStyleBackColor = false;
            btnRestore.Visible = false;
            btnRestore.Click += BtnRestore_Click;
            //
            // btnImportData
            //
            btnImportData.BackColor = Color.FromArgb(50, 50, 50);
            btnImportData.FlatAppearance.BorderColor = Color.Cyan;
            btnImportData.FlatStyle = FlatStyle.Flat;
            btnImportData.ForeColor = Color.LightSkyBlue;
            btnImportData.Location = new Point(483, 24);
            btnImportData.Margin = new Padding(4, 5, 4, 5);
            btnImportData.Name = "btnImportData";
            btnImportData.Size = new Size(138, 48);
            btnImportData.TabIndex = 4;
            btnImportData.Text = "导入数据";
            btnImportData.UseVisualStyleBackColor = false;
            btnImportData.Click += BtnImportData_Click;
            // 
            // lblTitle
            // 
            lblTitle.AutoSize = true;
            lblTitle.Font = new Font("Microsoft YaHei UI", 14F, FontStyle.Bold);
            lblTitle.ForeColor = Color.White;
            lblTitle.Location = new Point(16, 24);
            lblTitle.Margin = new Padding(4, 0, 4, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(325, 37);
            lblTitle.TabIndex = 1;
            lblTitle.Text = "脆性指数计算方法对比图";
            // 
            // pnlControls
            // 
            pnlControls.BackColor = Color.FromArgb(45, 45, 45);
            pnlControls.Controls.Add(btnSaveImage);
            pnlControls.Controls.Add(btnSeparate);
            pnlControls.Controls.Add(btnRestore);
            pnlControls.Controls.Add(btnImportData);
            pnlControls.Controls.Add(btnClose);
            pnlControls.Dock = DockStyle.Bottom;
            pnlControls.Location = new Point(0, 1184);
            pnlControls.Margin = new Padding(4, 5, 4, 5);
            pnlControls.Name = "pnlControls";
            pnlControls.Size = new Size(1650, 96);
            pnlControls.TabIndex = 2;
            // 
            //
            // chartComparison
            //
            chartComparison.BackColor = Color.FromArgb(45, 45, 45);
            chartComparison.BorderlineColor = Color.FromArgb(100, 100, 100);
            chartComparison.BorderlineDashStyle = ChartDashStyle.Solid;
            chartComparison.BorderlineWidth = 1;
            chartComparison.Location = new Point(16, 80);
            chartComparison.Name = "chartComparison";
            chartComparison.Size = new Size(1618, 1096);
            chartComparison.TabIndex = 3;
            chartComparison.Text = "chart1";
            //
            // SimpleComparisonChartForm
            //
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(1650, 1280);
            Controls.Add(chartComparison);
            Controls.Add(pnlControls);
            Controls.Add(lblTitle);
            ForeColor = Color.White;
            Margin = new Padding(4, 5, 4, 5);
            Name = "SimpleComparisonChartForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "脆性指数对比图";
            WindowState = FormWindowState.Maximized;
            pnlControls.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        private void InitializeChart()
        {
            // 清除现有内容
            chartComparison.Series.Clear();
            chartComparison.ChartAreas.Clear();
            chartComparison.Legends.Clear();

            // 创建图表区域 - 参考原系统ComparisonChartForm的布局
            ChartArea chartArea = new ChartArea("ComparisonArea");
            chartArea.BackColor = Color.FromArgb(45, 45, 45);

            // 设置图表区域位置和大小，确保不超出面板范围
            chartArea.Position = new ElementPosition(5, 5, 85, 90); // 左边距5%，上边距5%，宽度85%，高度90%
            chartArea.InnerPlotPosition = new ElementPosition(10, 10, 80, 80); // 内部绘图区域

            // 轴设置
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisX.MajorGrid.Enabled = true;
            chartArea.AxisY.MajorGrid.Enabled = true;
            chartArea.AxisY.IsReversed = true; // 深度轴反向显示

            // 设置轴范围，确保数据完全显示在图表内
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 10;

            // 设置边距，确保标签和标题完全显示
            chartArea.AxisX.LabelStyle.Angle = 0;
            chartArea.AxisY.LabelStyle.Angle = 0;
            chartArea.AxisX.TitleAlignment = StringAlignment.Center;
            chartArea.AxisY.TitleAlignment = StringAlignment.Center;

            chartComparison.ChartAreas.Add(chartArea);

            // 创建图例 - 参考原系统的图例布局
            Legend legend = new Legend("ComparisonLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            legend.Docking = Docking.Right;
            legend.Alignment = StringAlignment.Center;
            legend.Position = new ElementPosition(90, 10, 10, 80); // 右侧10%宽度用于图例
            chartComparison.Legends.Add(legend);
        }

        public void LoadComparisonData(string mineralDataPath, string staticDataPath, bool hasMineralData, bool hasStaticData)
        {
            try
            {
                int loadedSeries = 0;

                // 加载矿物组分法数据
                if (hasMineralData && File.Exists(mineralDataPath))
                {
                    LoadDataFromFile(mineralDataPath, Color.Blue, "矿物组分法");
                    loadedSeries++;
                }

                // 加载静态岩石力学参数法数据
                if (hasStaticData && File.Exists(staticDataPath))
                {
                    LoadDataFromFile(staticDataPath, Color.Cyan, "静态岩石力学参数法");
                    loadedSeries++;
                }

                // 更新标题
                if (loadedSeries > 0 && chartComparison?.Series != null)
                {
                    int totalPoints = chartComparison.Series.Sum(s => s?.Points?.Count ?? 0);
                    lblTitle.Text = $"脆性指数计算方法对比图 - 已加载 {loadedSeries} 个系统的数据，共 {totalPoints} 个数据点";
                }
                else
                {
                    lblTitle.Text = "脆性指数计算方法对比图 - 暂无数据";
                }

                // 确保按钮状态正确 - 参考原系统ComparisonChartForm
                if (loadedSeries >= 2)
                {
                    btnSeparate.Visible = true;
                    btnRestore.Visible = false;
                    isSeparated = false;
                }
                else
                {
                    btnSeparate.Visible = false;
                    btnRestore.Visible = false;
                }

                LoggingService.Instance.Info($"对比图数据加载完成，共 {loadedSeries} 个系列");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载对比数据失败: {ex.Message}");
                MessageBox.Show($"加载对比数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadDataFromFile(string filePath, Color seriesColor, string seriesName)
        {
            try
            {
                LoggingService.Instance.Info($"开始加载文件: {filePath}");

                if (!File.Exists(filePath))
                {
                    LoggingService.Instance.Warning($"文件不存在: {filePath}");
                    return;
                }

                if (chartComparison == null)
                {
                    LoggingService.Instance.Error("chartComparison为null");
                    return;
                }

                string jsonContent = File.ReadAllText(filePath);
                LoggingService.Instance.Info($"文件内容长度: {jsonContent.Length}");

                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    LoggingService.Instance.Warning("文件内容为空");
                    return;
                }

                dynamic data = JsonConvert.DeserializeObject(jsonContent);

                if (data == null)
                {
                    LoggingService.Instance.Warning("JSON反序列化结果为null");
                    return;
                }

                if (data.DataPoints == null)
                {
                    LoggingService.Instance.Warning("DataPoints为null");
                    return;
                }

                // 创建数据系列
                Series series = new Series(seriesName);
                series.ChartType = seriesName.Contains("静态") ? SeriesChartType.Spline : SeriesChartType.Line;
                series.Color = seriesColor;
                series.BorderWidth = 2;

                if (seriesName.Contains("静态"))
                {
                    series.MarkerStyle = MarkerStyle.None; // 静态岩石力学参数法不显示数据点
                }
                else
                {
                    series.MarkerStyle = MarkerStyle.Circle; // 矿物组分法显示圆形数据点
                    series.MarkerSize = 6;
                    series.MarkerColor = seriesColor;
                }

                // 添加数据点
                int pointCount = 0;
                foreach (var point in data.DataPoints)
                {
                    try
                    {
                        if (point?.BrittleIndex != null && point?.TopDepth != null)
                        {
                            double brittleIndex = Convert.ToDouble(point.BrittleIndex);
                            double depth = Convert.ToDouble(point.TopDepth);

                            if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                            {
                                series.Points.AddXY(brittleIndex, depth);
                                pointCount++;
                            }
                        }
                    }
                    catch (Exception pointEx)
                    {
                        LoggingService.Instance.Warning($"处理数据点时出错: {pointEx.Message}");
                    }
                }

                if (chartComparison.Series == null)
                {
                    LoggingService.Instance.Error("chartComparison.Series为null");
                    return;
                }

                chartComparison.Series.Add(series);
                LoggingService.Instance.Info($"已加载 {seriesName} 数据，共 {pointCount} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载文件 {filePath} 失败: {ex.Message}");
            }
        }

        private void BtnSaveImage_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "PNG图像|*.png|JPEG图像|*.jpg|所有文件|*.*";
                saveDialog.Title = "保存对比图";
                saveDialog.FileName = $"脆性指数对比图_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    chartComparison.SaveImage(saveDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show($"图像已保存到: {saveDialog.FileName}", "保存成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoggingService.Instance.Info($"对比图已保存到: {saveDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"保存对比图失败: {ex.Message}");
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 分隔显示按钮事件 - 参考原系统ComparisonChartForm
        /// </summary>
        private void BtnSeparate_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartComparison.Series.Count == 0)
                    return;

                // 清除现有图表区域
                chartComparison.ChartAreas.Clear();

                // 为每个系列创建单独的图表区域 - 参考原系统ComparisonChartForm
                int areaIndex = 0;
                int seriesCount = chartComparison.Series.Count;
                float areaHeight = 90f / seriesCount; // 每个区域的高度百分比
                float topMargin = 5f; // 顶部边距

                foreach (var series in chartComparison.Series)
                {
                    var chartArea = new ChartArea($"Area{areaIndex}")
                    {
                        BackColor = Color.FromArgb(40, 40, 40),
                        // 确保每个区域都在面板范围内，留出边距
                        Position = new ElementPosition(5, topMargin + areaIndex * areaHeight, 85, areaHeight - 2),
                        InnerPlotPosition = new ElementPosition(15, 10, 70, 80) // 内部绘图区域
                    };

                    // 设置轴样式
                    chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                    chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                    chartArea.AxisX.LineColor = Color.Gray;
                    chartArea.AxisY.LineColor = Color.Gray;
                    chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
                    chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
                    chartArea.AxisX.MajorGrid.Enabled = true;
                    chartArea.AxisY.MajorGrid.Enabled = true;
                    chartArea.AxisY.IsReversed = true;

                    // 设置轴范围
                    chartArea.AxisX.Minimum = 0;
                    chartArea.AxisX.Maximum = 100;
                    chartArea.AxisX.Interval = 20;

                    // 只在最后一个区域显示X轴标题
                    if (areaIndex == seriesCount - 1)
                    {
                        chartArea.AxisX.Title = "脆性指数 (%)";
                        chartArea.AxisX.TitleForeColor = Color.White;
                    }

                    chartArea.AxisY.Title = $"{series.Name}";
                    chartArea.AxisY.TitleForeColor = Color.White;

                    chartComparison.ChartAreas.Add(chartArea);
                    series.ChartArea = chartArea.Name;
                    areaIndex++;
                }

                isSeparated = true;
                btnSeparate.Visible = false;
                btnRestore.Visible = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"分隔显示失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 恢复显示按钮事件 - 参考原系统ComparisonChartForm
        /// </summary>
        private void BtnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartComparison.Series.Count == 0)
                    return;

                // 清除现有图表区域
                chartComparison.ChartAreas.Clear();

                // 创建单一图表区域 - 与InitializeChart保持一致
                var chartArea = new ChartArea("MainArea")
                {
                    BackColor = Color.FromArgb(45, 45, 45),
                    Position = new ElementPosition(5, 5, 85, 90),
                    InnerPlotPosition = new ElementPosition(10, 10, 80, 80)
                };

                // 设置轴样式
                chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                chartArea.AxisX.LineColor = Color.Gray;
                chartArea.AxisY.LineColor = Color.Gray;
                chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
                chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
                chartArea.AxisX.MajorGrid.Enabled = true;
                chartArea.AxisY.MajorGrid.Enabled = true;
                chartArea.AxisX.Title = "脆性指数 (%)";
                chartArea.AxisY.Title = "深度 (m)";
                chartArea.AxisX.TitleForeColor = Color.White;
                chartArea.AxisY.TitleForeColor = Color.White;
                chartArea.AxisY.IsReversed = true;

                // 设置轴范围
                chartArea.AxisX.Minimum = 0;
                chartArea.AxisX.Maximum = 100;
                chartArea.AxisX.Interval = 10;

                chartComparison.ChartAreas.Add(chartArea);

                // 将所有系列分配到同一个图表区域
                foreach (var series in chartComparison.Series)
                {
                    series.ChartArea = "MainArea";
                }

                isSeparated = false;
                btnSeparate.Visible = true;
                btnRestore.Visible = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"恢复显示失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入数据按钮事件 - 支持导入Excel、CSV文件和JSON对比数据
        /// </summary>
        private void BtnImportData_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "所有支持的文件|*.xlsx;*.xls;*.csv;*.json|Excel文件|*.xlsx;*.xls|CSV文件|*.csv|JSON对比数据|*.json";
                openDialog.Title = "选择要导入的数据文件";

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = openDialog.FileName;
                    string extension = Path.GetExtension(filePath).ToLower();

                    switch (extension)
                    {
                        case ".xlsx":
                        case ".xls":
                            ImportExcelData(filePath);
                            break;
                        case ".csv":
                            ImportCsvData(filePath);
                            break;
                        case ".json":
                            ImportJsonData(filePath);
                            break;
                        default:
                            MessageBox.Show("不支持的文件格式！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入数据失败: {ex.Message}");
                MessageBox.Show($"导入数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入Excel数据
        /// </summary>
        private void ImportExcelData(string filePath)
        {
            try
            {
                var dataSet = ReadExcelSheets(filePath);
                if (dataSet != null && dataSet.Tables.Count > 0)
                {
                    var dataTable = dataSet.Tables[0];

                    // 检测数据格式并转换为对比数据
                    var comparisonData = ConvertDataTableToComparisonData(dataTable, Path.GetFileNameWithoutExtension(filePath));

                    if (comparisonData != null)
                    {
                        // 添加到图表
                        AddComparisonDataToChart(comparisonData, Color.Orange, $"导入数据-{Path.GetFileNameWithoutExtension(filePath)}");
                        MessageBox.Show($"成功导入Excel数据，共 {comparisonData.DataPoints.Count} 个数据点", "导入成功",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入Excel数据失败: {ex.Message}");
                MessageBox.Show($"导入Excel数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入CSV数据
        /// </summary>
        private void ImportCsvData(string filePath)
        {
            try
            {
                var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                if (lines.Length < 2)
                {
                    MessageBox.Show("CSV文件格式不正确或没有数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var dataPoints = new List<dynamic>();

                // 跳过标题行，解析数据
                for (int i = 1; i < lines.Length; i++)
                {
                    var values = lines[i].Split(',');
                    if (values.Length >= 3)
                    {
                        try
                        {
                            double depth = Convert.ToDouble(values[0].Trim());
                            double brittleIndex = Convert.ToDouble(values[2].Trim()); // 假设第三列是脆性指数

                            if (depth > 0 && brittleIndex >= 0 && brittleIndex <= 100)
                            {
                                dataPoints.Add(new
                                {
                                    TopDepth = depth,
                                    BottomDepth = depth,
                                    BrittleIndex = brittleIndex
                                });
                            }
                        }
                        catch
                        {
                            // 跳过无效行
                        }
                    }
                }

                if (dataPoints.Count > 0)
                {
                    var comparisonData = new
                    {
                        SystemName = $"CSV导入-{Path.GetFileNameWithoutExtension(filePath)}",
                        DataPoints = dataPoints,
                        ImportTime = DateTime.Now
                    };

                    AddComparisonDataToChart(comparisonData, Color.Green, comparisonData.SystemName);
                    MessageBox.Show($"成功导入CSV数据，共 {dataPoints.Count} 个数据点", "导入成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("CSV文件中没有找到有效的数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入CSV数据失败: {ex.Message}");
                MessageBox.Show($"导入CSV数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入JSON对比数据
        /// </summary>
        private void ImportJsonData(string filePath)
        {
            try
            {
                string jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                dynamic data = JsonConvert.DeserializeObject(jsonContent);

                if (data?.DataPoints != null)
                {
                    string systemName = data.SystemName?.ToString() ?? Path.GetFileNameWithoutExtension(filePath);
                    AddComparisonDataToChart(data, Color.Purple, $"JSON导入-{systemName}");

                    int pointCount = 0;
                    foreach (var point in data.DataPoints)
                    {
                        pointCount++;
                    }

                    MessageBox.Show($"成功导入JSON对比数据，共 {pointCount} 个数据点", "导入成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("JSON文件格式不正确或没有数据点！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入JSON数据失败: {ex.Message}");
                MessageBox.Show($"导入JSON数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
